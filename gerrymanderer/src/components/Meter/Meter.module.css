.container {
  z-index: var(--layer-text);
  display: block;
  width: 12rem;
  padding: 0.5rem;
  border: 0.2rem solid var(--black);
  background: var(--white);
  box-shadow: 0.3rem 0.3rem 0 var(--black);
}

.meter {
  position: relative;
  display: flex;
  height: 1.5rem;
  padding: 0;
  margin: 0;
  background-color: var(--black);
}

.open,
.red,
.blue {
  display: block;
  transition: width 0.25s ease;
}

.open {
  width: 100%;
  height: 100%;
  flex-grow: 1;
  flex-shrink: 1;
  order: 2;
}

.red {
  height: 100%;
  order: 3;
  background: var(--red);
}

.blue {
  height: 100%;
  order: 1;
  background: var(--blue);
}

.ticks {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-between;
  inset: 0;
  pointer-events: none;
}

.tick {
  width: 0.125rem;
  height: 0.5rem;
  background: var(--white);
}

.tick:first-child,
.tick:last-child {
  background: none;
}
