import { PrismaClient } from "../../generated/prisma";
import { Request, Response, Router } from "express";
import { HttpError, HttpNotFoundError } from "../middlewares/errors";
import { body, param, validationResult } from "express-validator";
import { getHunt } from "../services/huntService";
import {
  generateQrCode,
  getNextOrdinal,
  isValidQrCode,
} from "../services/treasureService";

const router = Router({ mergeParams: true });
const prisma = new PrismaClient();

router.get(
  "/",
  [param("huntId").isNumeric().withMessage("huntId is invalid")],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    await getHunt(huntId);

    const treasures = await prisma.treasure.findMany({
      where: { huntId: huntId },
      include: { clue: true },
    });
    res.json(treasures);
  }
);

router.get(
  "/:treasureId",
  [
    param("huntId").isNumeric().withMessage("huntId is invalid"),
    param("treasureId").isNumeric().withMessage("treasureId is invalid"),
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    const treasureId = Number(req.params.treasureId);
    const treasure = await prisma.treasure.findUnique({
      where: { huntId: huntId, id: treasureId },
      include: { clue: true },
    });
    if (!treasure) {
      throw new HttpNotFoundError();
    }
    res.json(treasure);
  }
);

router.post(
  "/",
  [
    param("huntId").isNumeric().withMessage("huntId is invalid"),
    body("clue.text").notEmpty().withMessage("clue text is required"),
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    await getHunt(huntId);

    let qrCodeData: string = req.body.qrCodeData;
    if (!qrCodeData) {
      qrCodeData = generateQrCode();
    } else if (!isValidQrCode(qrCodeData)) {
      throw new HttpError("qrCodeData is invalid", 400);
    }

    let ordinal = await getNextOrdinal(huntId);
    const treasure = await prisma.treasure.create({
      data: {
        huntId: huntId,
        ordinal: ordinal,
        qrCodeData: qrCodeData,
      },
    });

    let clueText: string = req.body.clue.text;
    const clue = await prisma.clue.create({
      data: {
        treasureId: treasure.id,
        text: clueText,
      },
    });

    let result = await prisma.treasure.findUnique({
      where: { huntId: huntId, id: treasure.id },
      include: { clue: true },
    });

    res.status(201).json(result);
  }
);

router.patch(
  "/",
  [param("huntId").isNumeric().withMessage("huntId is invalid")],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    const treasureId = Number(req.params.treasureId);
    let treasure = await prisma.treasure.findUnique({
      where: { huntId: huntId, id: treasureId },
      include: { clue: true },
    });
    if (!treasure) {
      throw new HttpNotFoundError();
    }

    const data: { qrCodeData?: string } = {};
    if (req.body.qrCodeData) {
      let qrCodeData: string = req.body.qrCodeData;
      if (qrCodeData && !isValidQrCode(qrCodeData)) {
        throw new HttpError("qrCodeData is invalid", 400);
      }
      data["qrCodeData"] = qrCodeData;
    }

    if (req.body.clue.text) {
      data["clue"] = { text: req.body.clue.text };
    }

    res.status(200).json(treasure);
  }
);

export default router;
