import { PrismaClient } from "../../generated/prisma";
import { Request, Response, Router } from "express";
import { HttpNotFoundError } from "../middlewares/errors";
import { body, param, validationResult } from "express-validator";
import treasureRoutes from "./treasureRoutes";
import { getHunt } from "../services/huntService";

const router = Router();
const prisma = new PrismaClient();

router.get("/", async (req: Request, res: Response) => {
  const hunts = await prisma.hunt.findMany();
  res.json(hunts);
});

router.get(
  "/:huntId",
  [param("huntId").isNumeric().withMessage("huntId is invalid")],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    const hunt = await getHunt(huntId);
    res.json(hunt);
  }
);

router.post(
  "/",
  [body("title").notEmpty().withMessage("title is required")],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const title: string = req.body.title;
    const hunt = await prisma.hunt.create({
      data: { title },
    });
    res.status(201).json(hunt);
  }
);

router.patch(
  "/:huntId",
  [
    param("huntId").isNumeric().withMessage("huntId is invalid"),
    body("title").notEmpty().withMessage("title is required"),
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    const title: string = req.body.title;
    try {
      const hunt = await prisma.hunt.update({
        where: { id: huntId },
        data: { title },
      });
      res.json(hunt);
    } catch (error) {
      throw new HttpNotFoundError();
    }
  }
);

router.delete(
  "/:huntId",
  [param("huntId").isNumeric().withMessage("huntId is invalid")],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const huntId = Number(req.params.huntId);
    try {
      await prisma.hunt.delete({
        where: { id: huntId },
      });
      res.status(204).end();
    } catch (error) {
      throw new HttpNotFoundError();
    }
  }
);

router.use("/:huntId/treasures", treasureRoutes);

export default router;
