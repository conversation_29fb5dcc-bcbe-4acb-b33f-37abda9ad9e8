import express from "express";
import huntRoutes from "./routes/huntRoutes";
import { errorHandler, notFoundHandler } from "./middlewares/errors";

const app = express();

app.use(express.json());

app.use("/api/hunts", huntRoutes);
// app.use("/api/hunts", treasureRoutes);  not necessary because treasureRoutes is a child to huntRoutes

app.use(notFoundHandler);
app.use(errorHandler);

app.listen(3000, () => {
  console.log("Server started http://localhost:3000");
});
